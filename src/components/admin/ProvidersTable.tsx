
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useAuthHeader from 'react-auth-kit/hooks/useAuthHeader';
import { nullToUndefined } from '@/utils/typeHelpers';
import { providerService, Provider } from '@/services/providerService';
import { chatService } from '@/services/chatService';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MoreHorizontal, MessageSquare, Trash2, Eye, Building2, Link, Link2Off, UserCog } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import BusinessLinkingDialog from "./BusinessLinkingDialog";
import BusinessUnlinkingDialog from "./BusinessUnlinkingDialog";
import { ProviderTierManagementForm } from "./provider-tier/ProviderTierManagementForm";

interface ProviderStats {
  totalJobs: number;
  averageRating: number;
  revenue: number;
}

export const ProvidersTable: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [providerToDelete, setProviderToDelete] = useState<Provider | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerStats, setProviderStats] = useState<any | null>(null);
  const [isBusinessLinkingOpen, setIsBusinessLinkingOpen] = useState(false);
  const [isBusinessUnlinkingOpen, setIsBusinessUnlinkingOpen] = useState(false);
  const [isCreateChatModalOpen, setIsCreateChatModalOpen] = useState(false);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isAssignPlansOpen, setIsAssignPlansOpen] = useState(false);
  const authHeader = useAuthHeader();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Fetch providers function
  const fetchProviders = async () => {
    setIsLoading(true);
    try {
      const response = await providerService.getProviders(1, 10, undefined, nullToUndefined(authHeader) || '');
      
      if (response.isSuccess && response.data) {
        setProviders(response.data.data);
      } else {
        toast.error(response.error || 'Failed to fetch providers');
      }
    } catch (error) {
      console.error('Error fetching providers:', error);
      toast.error('Failed to fetch providers');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch providers on component mount
  useEffect(() => {
    fetchProviders();
  }, [authHeader]);
  
  const fetchProviderStats = async (providerId: string) => {
    try {
      const response = await providerService.getProviderStats(providerId, nullToUndefined(authHeader) || '');
      
      if (response.isSuccess && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching provider stats:', error);
      return null;
    }
  };

  const handleViewDetails = async (provider: Provider) => {
    setSelectedProvider(provider);
    
    const stats = await fetchProviderStats(provider.id);
    if (stats) {
      setProviderStats(stats);
    }
    
    setIsDetailModalOpen(true);
  };

  // Handle provider deletion with proper type handling
  const handleDeleteProvider = async () => {
    if (!providerToDelete) return;

    setIsDeleting(true);
    try {
      const response = await providerService.deleteProvider(
        providerToDelete.id,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess) {
        toast.success('Provider deleted successfully');
        setProviders(providers.filter(p => p.id !== providerToDelete.id));
        setIsDeleteModalOpen(false);
        setProviderToDelete(null);
      } else {
        toast.error(response.error || 'Failed to delete provider');
      }
    } catch (error) {
      console.error('Error deleting provider:', error);
      toast.error('Failed to delete provider');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle send message to provider
  const handleSendMessage = (provider: Provider) => {
    setSelectedProvider(provider);
    setIsCreateChatModalOpen(true);
  };

  // Handle creating chat room with provider
  const handleCreateChat = async () => {
    if (!selectedProvider || !user || !user.id) {
      toast.error('Unable to identify current user. Please try logging in again.');
      return;
    }

    setIsCreatingChat(true);

    try {
      const response = await chatService.createChat(
        selectedProvider.id,
        'direct',
        undefined,
        nullToUndefined(authHeader) || ''
      );

      if (response.isSuccess && response.data) {
        toast.success(`Chat room created with ${selectedProvider.name}`);
        setIsCreateChatModalOpen(false);
        setSelectedProvider(null);
        navigate('/admin/messages');
      } else {
        if (response.error?.includes('already exists') || response.error?.includes('duplicate')) {
          toast.success(`Opening existing chat with ${selectedProvider.name}`);
          setIsCreateChatModalOpen(false);
          setSelectedProvider(null);
          navigate('/admin/messages');
        } else {
          toast.error(response.error || 'Failed to create chat room');
        }
      }
    } catch (error) {
      toast.error('An error occurred while creating the chat room');
    } finally {
      setIsCreatingChat(false);
    }
  };

  if (isLoading) {
    return <div>Loading providers...</div>;
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {providers.map((provider) => (
            <TableRow key={provider.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Avatar>
                    <AvatarImage src={provider.avatar || "/placeholder.svg"} alt={provider.name} />
                    <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  {provider.name}
                </div>
              </TableCell>
              <TableCell>{provider.email}</TableCell>
              <TableCell>{provider.phone}</TableCell>
              <TableCell>
                {provider.is_active ? (
                  <Badge variant="outline">Active</Badge>
                ) : (
                  <Badge>Inactive</Badge>
                )}
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDetails(provider)}>
                      <Eye className="mr-2 h-4 w-4" /> View Details
                    </DropdownMenuItem>
                    {
                      provider.business ? <DropdownMenuItem onClick={() => {
                        setSelectedProvider(provider);
                        setIsBusinessUnlinkingOpen(true);
                      }}>
                        <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                      </DropdownMenuItem> :
                      <DropdownMenuItem onClick={() => {
                        setSelectedProvider(provider);
                        setIsBusinessLinkingOpen(true);
                      }}>
                        <Building2 className="mr-2 h-4 w-4" /> Link Business
                      </DropdownMenuItem>
                    }
                    <DropdownMenuItem onClick={() => {
                      setSelectedProvider(provider);
                      setIsAssignPlansOpen(true);
                    }}>
                      <UserCog className="mr-2 h-4 w-4" /> Assign Plans
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem onClick={() => handleSendMessage(provider)}>
                      <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setProviderToDelete(provider);
                        setIsDeleteModalOpen(true);
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Provider Details Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="sm:max-w-[625px]">
          <DialogHeader>
            <DialogTitle>Provider Details</DialogTitle>
            <DialogDescription>
              View detailed information about the selected provider.
            </DialogDescription>
          </DialogHeader>
          {selectedProvider && providerStats && (
            <ScrollArea className="h-[400px] w-full space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-lg font-semibold">Personal Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Name:</strong> {selectedProvider.name}</p>
                    <p><strong>Email:</strong> {selectedProvider.email}</p>
                    <p><strong>Phone:</strong> {selectedProvider.phone}</p>
                    <p><strong>Location:</strong> {selectedProvider.location}</p>
                  </div>
                </div>
                <div>
                  <div className="text-lg font-semibold">Business Information</div>
                  <div className="mt-2 space-y-1">
                    <p><strong>Business Name:</strong> {selectedProvider.business_name || 'Not linked'}</p>
                    <p><strong>Category:</strong> {selectedProvider.category || 'N/A'}</p>
                    <p><strong>Specialization:</strong> {selectedProvider.specialty || 'N/A'}</p>
                    <div className="flex space-x-2 mt-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBusinessLinkingOpen(true)}
                      >
                        <Link className="mr-2 h-4 w-4" /> Link Business
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsBusinessUnlinkingOpen(true)}
                      >
                        <Link2Off className="mr-2 h-4 w-4" /> Unlink Business
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="text-lg font-semibold">Statistics</div>
                <div className="mt-2 space-y-1">
                  <p><strong>Total Jobs:</strong> {providerStats.totalJobs}</p>
                  <p><strong>Average Rating:</strong> {providerStats.averageRating}</p>
                  <p><strong>Revenue:</strong> ${providerStats.revenue}</p>
                </div>
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the provider.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isDeleting}
              onClick={handleDeleteProvider}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Business Linking Dialog */}
      <BusinessLinkingDialog
        isOpen={isBusinessLinkingOpen}
        onClose={() => setIsBusinessLinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh providers data after successful business linking
          fetchProviders();
          setIsBusinessLinkingOpen(false);
        }}
      />

      {/* Business Unlinking Dialog */}
      <BusinessUnlinkingDialog
        isOpen={isBusinessUnlinkingOpen}
        onClose={() => setIsBusinessUnlinkingOpen(false)}
        provider={selectedProvider}
        onSuccess={() => {
          // Refresh providers data after successful business unlinking
          fetchProviders();
          setIsBusinessUnlinkingOpen(false);
        }}
      />

      {/* Create Chat Confirmation Dialog */}
      <AlertDialog open={isCreateChatModalOpen} onOpenChange={setIsCreateChatModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Create Chat Room</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedProvider && (
                <>
                  Create a direct chat room with <strong>{selectedProvider.name}</strong>?
                  This will allow you to communicate directly with this provider.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isCreatingChat}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isCreatingChat}
              onClick={handleCreateChat}
            >
              {isCreatingChat ? "Creating..." : "Create Chat Room"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Assign Plans Dialog */}
      <Dialog open={isAssignPlansOpen} onOpenChange={setIsAssignPlansOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Plan to Provider</DialogTitle>
            <DialogDescription>
              {selectedProvider && (
                <>
                  Assign subscription plans to <strong>{selectedProvider.name}</strong> and manage their tier status.
                </>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <ProviderTierManagementForm 
              selectedProvider={selectedProvider || undefined}
              onSuccess={() => {
                // Refresh providers data after successful plan assignment
                fetchProviders();
                setIsAssignPlansOpen(false);
                setSelectedProvider(null);
              }} 
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
