
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  MessageSquare,
  Calendar,
  ArrowLeft,
  MapPin,
  DollarSign,
  Clock,
  AlertCircle,
  Loader2,
  Star,
  X,
  CheckCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { apiService } from "@/services/api";
import { Bid } from '@/types/bid';
import { acceptBid, rejectBid } from "@/services/bidService";

interface JobDetail {
  id: string;
  title: string;
  description: string;
  provider?: {
    name: string;
    avatar?: string;
    email?: string;
    phone?: string;
  };
  location: {
    address: string;
    city: string;
    state: string;
  };
  schedule: {
    date: string;
    time?: string;
  };
  budget: number;
  status: string;
  createdAt: string;
  service: {
    category: string;
    tasks: string[];
  };
}

export function JobManage() {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useAuth();

  const [job, setJob] = useState<JobDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [bids, setBids] = useState<Bid[]>([]);
  const [bidsLoading, setBidsLoading] = useState(false);
  const [isAccepting, setIsAccepting] = useState<string | null>(null);
  const [isRejecting, setIsRejecting] = useState<string | null>(null);

  const fetchJobDetails = useCallback(async () => {
    if (!jobId || !token) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService<{ data: any }>(`/api/job-bookings/${jobId}`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (response.isSuccess && response.data) {
        // Transform API response to match our interface
        const jobData = response.data.data;
        setJob({
          id: jobData.id,
          title: jobData.service?.tasks?.join(", ") || jobData.service?.category || 'Service Request',
          description: jobData.description || 'No description provided',
          provider: jobData.contact ? {
            name: jobData.contact.fullName || 'Provider',
            avatar: jobData.contact.avatar,
            email: jobData.contact.email,
            phone: jobData.contact.phone
          } : undefined,
          location: {
            address: jobData.location?.address || '',
            city: jobData.location?.city || '',
            state: jobData.location?.state || ''
          },
          schedule: {
            date: jobData.schedule?.date || jobData.createdAt,
            time: jobData.schedule?.time
          },
          budget: jobData.budget || 0,
          status: jobData.status || 'Active',
          createdAt: jobData.createdAt,
          service: {
            category: jobData.service?.category || 'General Service',
            tasks: jobData.service?.tasks || []
          }
        });
      }
    } catch (error) {
      console.error('Error fetching job details:', error);
      toast({
        title: "Error",
        description: "Failed to load job details",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [jobId, token, toast]);

  const fetchBids = useCallback(async () => {
    if (!jobId || !token) return;

    setBidsLoading(true);
    try {
      // Use the correct customer route for fetching bids
      const bidsResponse = await apiService<{ data: any[] }>(`/api/customer/job-bookings/${jobId}/bids`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (bidsResponse.isSuccess && bidsResponse.data) {
        // The API returns data directly in the data array
        const bidsArray = Array.isArray(bidsResponse.data) ? bidsResponse.data : bidsResponse.data.data || [];

        // Transform bids data to match Bid interface based on actual API response
        const transformedBids = bidsArray.map((bid: any) => ({
          id: bid.id,
          jobId: bid.job_booking_id || jobId,
          providerId: bid.provider_id || bid.provider?.id,
          customerId: bid.customer_id,
          amount: bid.amount || bid.bid_amount || 0,
          description: bid.description || bid.cover_letter || 'No description provided',
          status: bid.status === 'requested' ? 'requested' : bid.status || 'pending',
          submittedAt: bid.created_at,
          updatedAt: bid.updated_at,
          createdAt: bid.created_at,
          provider: bid.provider || bid.bidder ? {
            id: (bid.provider || bid.bidder).id,
            firstName: (bid.provider || bid.bidder).first_name || (bid.provider || bid.bidder).name?.split(' ')[0] || 'Unknown',
            lastName: (bid.provider || bid.bidder).last_name || (bid.provider || bid.bidder).name?.split(' ')[1] || 'Provider',
            businessName: (bid.provider || bid.bidder).business_name,
            avatar: (bid.provider || bid.bidder).avatar,
            rating: (bid.provider || bid.bidder).rating || 0,
            specialty: (bid.provider || bid.bidder).specialty || []
          } : undefined
        }));
        setBids(transformedBids);
      } else {
        console.error('Failed to fetch bids:', bidsResponse.error);
        toast({
          title: "Error",
          description: bidsResponse.error || "Failed to load bids",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error fetching bids:', error);
      toast({
        title: "Error",
        description: "Failed to load bids",
        variant: "destructive"
      });
    } finally {
      setBidsLoading(false);
    }
  }, [jobId, token, toast]);

  useEffect(() => {
    fetchJobDetails();
    fetchBids();
  }, [fetchJobDetails, fetchBids]);

  const handleAcceptBid = async (bidId: string) => {
    if (!token || !jobId) return;

    setIsAccepting(bidId);
    try {
      const response = await acceptBid(jobId, bidId, token);

      if (response.isSuccess) {
        toast({
          title: "Success",
          description: "Bid accepted successfully!"
        });
        fetchBids(); // Refresh bids data
        fetchJobDetails(); // Refresh job data in case status changed
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to accept bid",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error accepting bid:', error);
      toast({
        title: "Error",
        description: "Failed to accept bid",
        variant: "destructive"
      });
    } finally {
      setIsAccepting(null);
    }
  };

  const handleRejectBid = async (bidId: string) => {
    if (!token || !jobId) return;

    setIsRejecting(bidId);
    try {
      const response = await rejectBid(jobId, bidId, token);

      if (response.isSuccess) {
        toast({
          title: "Success",
          description: "Bid rejected successfully!"
        });
        fetchBids(); // Refresh bids data
        fetchJobDetails(); // Refresh job data in case status changed
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to reject bid",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error rejecting bid:', error);
      toast({
        title: "Error",
        description: "Failed to reject bid",
        variant: "destructive"
      });
    } finally {
      setIsRejecting(null);
    }
  };

  const handleBack = () => {
    navigate('/customer/dashboard/active-jobs');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatBidDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={handleBack} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Active Jobs
        </Button>
        <Card>
          <CardContent className="py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Job not found</h3>
            <p className="text-muted-foreground">
              This job may have been removed or you don't have access to it.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack} size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Active Jobs
          </Button>
          <Badge variant={job.status === 'Active' ? 'default' : 'secondary'}>
            {job.status}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Job Details */}
        <div className="xl:col-span-3 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">{job.title}</CardTitle>
              <p className="text-muted-foreground">{job.service.category}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-muted-foreground">{job.description}</p>
              </div>

              {job.service.tasks.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tasks</h4>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    {job.service.tasks.map((task, index) => (
                      <li key={index}>{task}</li>
                    ))}
                  </ul>
                </div>
              )}

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {job.location.address && `${job.location.address}, `}
                      {job.location.city}, {job.location.state}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Scheduled</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(job.schedule.date)}
                      {job.schedule.time && ` at ${job.schedule.time}`}
                    </p>
                  </div>
                </div>

                {job.budget > 0 && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Budget</p>
                      <p className="text-sm text-muted-foreground">
                        ${job.budget.toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Posted</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(job.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Job Actions Sidebar */}
        <div className="xl:col-span-1">
          <div className="sticky top-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Job Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full" variant="destructive" size="sm">
                  Cancel Job
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Bids Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            Bids Received {bids.length > 0 && `(${bids.length})`}
          </h2>
          {bidsLoading && (
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
          )}
        </div>

        {bids.length === 0 && !bidsLoading ? (
          <Card>
            <CardContent className="py-8 text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No bids yet</h3>
              <p className="text-muted-foreground mb-4">
                We'll notify you when professionals respond to your job request!
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {bids.map((bid) => (
              <Card key={bid.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={bid.provider?.avatar} />
                        <AvatarFallback>
                          {bid.provider ?
                            `${bid.provider.firstName[0]}${bid.provider.lastName[0]}` :
                            'P'
                          }
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">
                          {bid.provider ?
                            `${bid.provider.firstName} ${bid.provider.lastName}` :
                            'Unknown Provider'
                          }
                        </h3>
                        {bid.provider && (
                          <div className="flex items-center gap-2">
                            <div className="flex">{renderStars(bid.provider.rating)}</div>
                            <span className="text-sm text-muted-foreground">
                              ({bid.provider.rating}/5)
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">
                        ${bid.amount.toLocaleString()}
                      </div>
                      <Badge
                        variant={
                          bid.status === 'accepted' ? 'default' :
                          bid.status === 'rejected' ? 'secondary' :
                          'outline'
                        }
                        className={
                          bid.status === 'accepted' ? 'bg-green-100 text-green-800' :
                          bid.status === 'rejected' ? 'bg-red-100 text-red-800' : ''
                        }
                      >
                        {bid.status === 'accepted' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {bid.status === 'rejected' && <X className="h-3 w-3 mr-1" />}
                        {bid.status === 'requested' ? 'Pending Review' : bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Proposal</h4>
                    <p className="text-muted-foreground">{bid.description}</p>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>Submitted {formatBidDate(bid.createdAt)}</span>
                    </div>

                    {(bid.status === 'pending' || bid.status === 'requested' || (bid.status !== 'accepted' && bid.status !== 'rejected')) && (
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          <span className="hidden sm:inline">Message Provider</span>
                          <span className="sm:hidden">Message</span>
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleAcceptBid(bid.id)}
                          disabled={isAccepting === bid.id || isRejecting === bid.id}
                          className="bg-green-600 hover:bg-green-700 flex-1 sm:flex-none"
                        >
                          {isAccepting === bid.id ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <CheckCircle className="h-4 w-4 mr-2" />
                          )}
                          Accept
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleRejectBid(bid.id)}
                          disabled={isAccepting === bid.id || isRejecting === bid.id}
                          className="flex-1 sm:flex-none"
                        >
                          {isRejecting === bid.id ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <X className="h-4 w-4 mr-2" />
                          )}
                          Reject
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
