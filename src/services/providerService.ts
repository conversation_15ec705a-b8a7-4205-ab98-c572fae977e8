import { ApiResponse, apiService } from './api';

interface Plan {
  id: number;
  name: string;
  max_services: number;
  max_addresses: number;
  max_servicemen: number;
  max_service_packages: number;
  price: string;
  duration: string;
  description: string;
  status: number;
  created_by: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

interface CurrentSubscriptionType {
  plan: Plan;
  start_date: string;
  end_date: string;
  is_active: number;
  allowed_max_services: number;
  allowed_max_addresses: number;
  allowed_max_servicemen: number;
  allowed_max_service_packages: number;
}

// Provider interfaces
export interface Provider {
  id: string;
  name: string;
  email: string;
  phone: string;
  role_id: number;
  created_at: string;
  updated_at: string;
  status?: string;
  avatar?: string;
  specialty?: string;
  location?: string;
  plan?: string;
  rating?: number;
  is_active?: boolean; // Added
  business_name?: string; // Added
  category?: string; // Added
  business?: BusinessType;
  current_subscription?: CurrentSubscriptionType;
}

interface BusinessType {
  businessId: string;
  name: string;
  category: string;
  location: string;
  address: string;
  phone: string;
  website: string;
  email: string | null;
  hours: string;
  photos: string; 
  services: string;
  reviews: string;
  lat: string;
  lng: string;
  createdAt: string;
  updatedAt: string;
  certificates: any[];
  certificates_status: string | null;
}

export interface ProviderResponse {
  data: Provider[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

// Request interfaces
export interface CreateProviderRequest {
  name: string;
  email: string;
  phone?: string;
  password: string;
  password_confirmation: string;
  role_id: number;
}

export interface UpdateProviderRequest {
  name?: string;
  email?: string;
  phone?: string;
  password?: string;
  password_confirmation?: string;
  role_id?: number;
}

// Interface for items that just have an id and name (e.g., roles, specializations, business types)
export interface NamedId {
  id: number;
  name: string;
}

// Response type for specializations
export interface SpecializationsResponse {
  data: NamedId[];
}

// Response type for business types
export interface BusinessTypesResponse {
  data: NamedId[];
}

// Interface for Provider Statistics
export interface ProviderStats {
  totalJobs: number;
  averageRating: number;
  revenue: number;
  // Add other relevant stats fields
}

// Provider service with API functions
export const providerService = {
  // Create a new provider
  createProvider: (data: CreateProviderRequest, token?: string): Promise<ApiResponse<Provider>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Provider>('/api/user', { // Assuming endpoint is /api/user for providers too
      method: 'POST',
      body: data,
      requiresAuth: true,
      headers,
    });
  },

  // Get all providers with optional filtering and pagination
  getProviders: async (
    page: number = 1,
    perPage: number = 10,
    search?: string,
    token?: string
  ): Promise<ApiResponse<ProviderResponse>> => {
    let endpoint = `/api/user?role_id=4&page=${page}&per_page=${perPage}`; // Assuming role_id=3 for providers

    if (search) {
      endpoint += `&search=${encodeURIComponent(search)}`;
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<ProviderResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get a single provider by ID
  getProvider: (id: string, token?: string): Promise<ApiResponse<Provider>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Provider>(`/api/user/${id}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Update a provider
  updateProvider: (id: string, data: UpdateProviderRequest, token?: string): Promise<ApiResponse<Provider>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<Provider>(`/api/user/${id}`, {
      method: 'PUT',
      body: data,
      requiresAuth: true,
      headers,
    });
  },

  // Delete a provider
  deleteProvider: (id: string, token?: string): Promise<ApiResponse<unknown>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService(`/api/user/${id}`, {
      method: 'DELETE',
      requiresAuth: true,
      headers,
    });
  },

  // Get public roles
  getPublicRoles: (token?: string): Promise<ApiResponse<NamedId[]>> => { // Assuming roles are NamedId[]
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<NamedId[]>('/api/roles-public', { // Assuming this endpoint returns NamedId[]
      method: 'GET',
      headers,
    });
  },

  // Get specializations (Placeholder - replace with actual endpoint)
  getSpecializations: (token?: string): Promise<ApiResponse<SpecializationsResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }
    // Replace with actual endpoint for specializations
    return apiService<SpecializationsResponse>('/api/specializations', {
      method: 'GET',
      headers, // Assuming public or requires token based on your API
    });
  },

  // Get business types (Placeholder - replace with actual endpoint)
  getBusinessTypes: (token?: string): Promise<ApiResponse<BusinessTypesResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }
    // Replace with actual endpoint for business types
    return apiService<BusinessTypesResponse>('/api/business-types', {
      method: 'GET',
      headers, // Assuming public or requires token based on your API
    });
  },

  // Get provider statistics (Placeholder - replace with actual endpoint)
  getProviderStats: (providerId: string, token?: string): Promise<ApiResponse<ProviderStats>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }
    // Replace with actual endpoint for provider stats
    return apiService<ProviderStats>(`/api/providers/${providerId}/stats`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};
